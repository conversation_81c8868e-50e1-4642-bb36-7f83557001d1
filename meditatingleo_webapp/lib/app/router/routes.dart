/// Route definitions for the MeditatingLeo Web Application.
///
/// This file contains all the route paths and names used throughout
/// the application for consistent navigation and deep linking.
class AppRoutes {
  // Private constructor to prevent instantiation
  AppRoutes._();

  /// Home route - main landing page
  static const String home = '/';
  static const String homeName = 'home';

  /// Authentication routes
  static const String login = '/login';
  static const String loginName = 'login';
  static const String register = '/register';
  static const String registerName = 'register';
  static const String forgotPassword = '/forgot-password';
  static const String forgotPasswordName = 'forgot-password';
  static const String resetPassword = '/reset-password';
  static const String resetPasswordName = 'reset-password';

  /// Journal routes
  static const String journal = '/journal';
  static const String journalName = 'journal';
  static const String journalEntry = '/journal/entry/:id';
  static const String journalEntryName = 'journal-entry';
  static const String newJournalEntry = '/journal/new';
  static const String newJournalEntryName = 'new-journal-entry';

  /// Journey routes
  static const String journeys = '/journeys';
  static const String journeysName = 'journeys';
  static const String journey = '/journeys/:id';
  static const String journeyName = 'journey';
  static const String journeyPrompt = '/journeys/:journeyId/prompt/:promptId';
  static const String journeyPromptName = 'journey-prompt';

  /// Goals routes
  static const String goals = '/goals';
  static const String goalsName = 'goals';
  static const String goal = '/goals/:id';
  static const String goalName = 'goal';
  static const String newGoal = '/goals/new';
  static const String newGoalName = 'new-goal';

  /// Habits routes
  static const String habits = '/habits';
  static const String habitsName = 'habits';
  static const String habit = '/habits/:id';
  static const String habitName = 'habit';
  static const String newHabit = '/habits/new';
  static const String newHabitName = 'new-habit';

  /// Focus timer routes
  static const String focusTimer = '/focus';
  static const String focusTimerName = 'focus-timer';
  static const String focusSession = '/focus/session/:id';
  static const String focusSessionName = 'focus-session';

  /// Profile and settings routes
  static const String profile = '/profile';
  static const String profileName = 'profile';
  static const String settings = '/settings';
  static const String settingsName = 'settings';
  static const String preferences = '/settings/preferences';
  static const String preferencesName = 'preferences';
  static const String account = '/settings/account';
  static const String accountName = 'account';

  /// Analytics and insights routes
  static const String insights = '/insights';
  static const String insightsName = 'insights';
  static const String analytics = '/analytics';
  static const String analyticsName = 'analytics';

  /// Help and support routes
  static const String help = '/help';
  static const String helpName = 'help';
  static const String about = '/about';
  static const String aboutName = 'about';
  static const String privacy = '/privacy';
  static const String privacyName = 'privacy';
  static const String terms = '/terms';
  static const String termsName = 'terms';

  /// Error routes
  static const String notFound = '/404';
  static const String notFoundName = 'not-found';
  static const String error = '/error';
  static const String errorName = 'error';

  /// Utility methods for route generation

  /// Generates a journal entry route with the given ID
  static String journalEntryRoute(String id) {
    return journalEntry.replaceAll(':id', id);
  }

  /// Generates a journey route with the given ID
  static String journeyRoute(String id) {
    return journey.replaceAll(':id', id);
  }

  /// Generates a journey prompt route with the given IDs
  static String journeyPromptRoute(String journeyId, String promptId) {
    return journeyPrompt
        .replaceAll(':journeyId', journeyId)
        .replaceAll(':promptId', promptId);
  }

  /// Generates a goal route with the given ID
  static String goalRoute(String id) {
    return goal.replaceAll(':id', id);
  }

  /// Generates a habit route with the given ID
  static String habitRoute(String id) {
    return habit.replaceAll(':id', id);
  }

  /// Generates a focus session route with the given ID
  static String focusSessionRoute(String id) {
    return focusSession.replaceAll(':id', id);
  }

  /// Returns all public routes (accessible without authentication)
  static List<String> get publicRoutes => [
        login,
        register,
        forgotPassword,
        resetPassword,
        about,
        privacy,
        terms,
        help,
      ];

  /// Returns all protected routes (require authentication)
  static List<String> get protectedRoutes => [
        home,
        journal,
        journalEntry,
        newJournalEntry,
        journeys,
        journey,
        journeyPrompt,
        goals,
        goal,
        newGoal,
        habits,
        habit,
        newHabit,
        focusTimer,
        focusSession,
        profile,
        settings,
        preferences,
        account,
        insights,
        analytics,
      ];
}
