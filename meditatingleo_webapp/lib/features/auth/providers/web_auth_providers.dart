import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../data/repositories/web_auth_repository.dart';
import '../data/services/web_auth_service.dart';
import '../data/services/web_session_service.dart';
import '../data/models/web_user_model.dart';
import '../data/models/auth_session_model.dart';
import '../../../shared/providers/app_providers.dart';
import '../../../shared/models/result.dart';
import '../../../shared/models/app_error.dart';

part 'web_auth_providers.g.dart';

/// Authentication state model
class WebAuthState {
  final WebUserModel? user;
  final AuthSessionModel? session;
  final bool isLoading;
  final String? error;

  const WebAuthState({
    this.user,
    this.session,
    this.isLoading = false,
    this.error,
  });

  bool get isAuthenticated =>
      user != null && session != null && session!.isValid;
  bool get isEmailConfirmed => user?.isEmailConfirmed ?? false;

  WebAuthState copyWith({
    WebUserModel? user,
    AuthSessionModel? session,
    bool? isLoading,
    String? error,
  }) {
    return WebAuthState(
      user: user ?? this.user,
      session: session ?? this.session,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }

  WebAuthState clearError() {
    return copyWith(error: null);
  }

  WebAuthState setLoading(bool loading) {
    return copyWith(isLoading: loading);
  }

  WebAuthState setError(String error) {
    return copyWith(error: error, isLoading: false);
  }

  WebAuthState clearAuth() {
    return const WebAuthState();
  }
}

/// Web authentication service provider
@riverpod
WebAuthService webAuthService(WebAuthServiceRef ref) {
  final supabase = ref.watch(supabaseProvider);
  return WebAuthService(supabase);
}

/// Web session service provider
@riverpod
WebSessionService webSessionService(WebSessionServiceRef ref) {
  return WebSessionService.web();
}

/// Web authentication repository provider
@riverpod
WebAuthRepository webAuthRepository(WebAuthRepositoryRef ref) {
  final authService = ref.watch(webAuthServiceProvider);
  final sessionService = ref.watch(webSessionServiceProvider);
  return WebAuthRepository(authService, sessionService);
}

/// Main web authentication notifier
@riverpod
class WebAuthNotifier extends _$WebAuthNotifier {
  @override
  AsyncValue<WebAuthState> build() {
    // Initialize with empty state
    final initialState = const WebAuthState();

    // Try to restore session on startup
    _restoreSession();

    return AsyncValue.data(initialState);
  }

  /// Signs in with email and password
  Future<void> signInWithEmail(
    String email,
    String password, {
    bool rememberMe = false,
  }) async {
    state = AsyncValue.data(state.value!.setLoading(true));

    try {
      final repository = ref.read(webAuthRepositoryProvider);
      final result = await repository.signInWithEmail(
        email,
        password,
        rememberMe: rememberMe,
      );

      if (result.isSuccess) {
        final data = result.data!;
        final user = data.$1;
        final session = data.$2;
        state = AsyncValue.data(
          WebAuthState(user: user, session: session),
        );
      } else {
        state = AsyncValue.data(
          state.value!.setError(result.error!.message),
        );
      }
    } catch (e) {
      state = AsyncValue.data(
        state.value!.setError('Sign in failed: ${e.toString()}'),
      );
    }
  }

  /// Signs up a new user and auto-login if session is available
  Future<void> signUp(String email, String password, String? name) async {
    state = AsyncValue.data(state.value!.setLoading(true));

    try {
      final repository = ref.read(webAuthRepositoryProvider);
      final result = await repository.signUp(email, password, name);

      if (result.isSuccess) {
        final (user, session) = result.data!;
        state = AsyncValue.data(
          WebAuthState(user: user, session: session),
        );
      } else {
        state = AsyncValue.data(
          state.value!.setError(result.error!.message),
        );
      }
    } catch (e) {
      state = AsyncValue.data(
        state.value!.setError('Sign up failed: ${e.toString()}'),
      );
    }
  }

  /// Signs out the current user
  Future<void> signOut() async {
    state = AsyncValue.data(state.value!.setLoading(true));

    try {
      final repository = ref.read(webAuthRepositoryProvider);
      final result = await repository.signOut();

      if (result.isSuccess) {
        state = AsyncValue.data(const WebAuthState());
      } else {
        state = AsyncValue.data(
          state.value!.setError(result.error!.message),
        );
      }
    } catch (e) {
      state = AsyncValue.data(
        state.value!.setError('Sign out failed: ${e.toString()}'),
      );
    }
  }

  /// Sends password reset email
  Future<Result<void, AppError>> resetPassword(String email) async {
    try {
      final repository = ref.read(webAuthRepositoryProvider);
      return await repository.resetPassword(email);
    } catch (e) {
      return Result.failure(
        AppError.unknown('Password reset failed: ${e.toString()}'),
      );
    }
  }

  /// Refreshes the current session
  Future<void> refreshSession() async {
    try {
      final repository = ref.read(webAuthRepositoryProvider);
      final result = await repository.refreshSession();

      if (result.isSuccess) {
        final newSession = result.data;
        state = AsyncValue.data(
          state.value!.copyWith(session: newSession),
        );
      } else {
        // Session refresh failed, sign out
        await signOut();
      }
    } catch (e) {
      // Session refresh failed, sign out
      await signOut();
    }
  }

  /// Updates user profile
  Future<void> updateProfile({String? name, String? avatarUrl}) async {
    try {
      final repository = ref.read(webAuthRepositoryProvider);
      final result = await repository.updateProfile(
        name: name,
        avatarUrl: avatarUrl,
      );

      if (result.isSuccess) {
        final updatedUser = result.data;
        state = AsyncValue.data(
          state.value!.copyWith(user: updatedUser),
        );
      } else {
        state = AsyncValue.data(
          state.value!.setError(result.error!.message),
        );
      }
    } catch (e) {
      state = AsyncValue.data(
        state.value!.setError('Profile update failed: ${e.toString()}'),
      );
    }
  }

  /// Clears any error state
  void clearError() {
    if (state.hasValue) {
      state = AsyncValue.data(state.value!.clearError());
    }
  }

  /// Resends email verification for the given email address.
  Future<Result<void, AppError>> resendEmailVerification(String email) async {
    try {
      final repository = ref.read(webAuthRepositoryProvider);
      return await repository.resendEmailVerification(email);
    } catch (e) {
      return Result.failure(
        AppError.unknown(
            'Failed to resend verification email: ${e.toString()}'),
      );
    }
  }

  /// Verifies email with the provided token.
  Future<Result<void, AppError>> verifyEmail(String token) async {
    try {
      final repository = ref.read(webAuthRepositoryProvider);
      final result = await repository.verifyEmail(token);

      if (result.isSuccess && state.value?.user != null) {
        // Update user state to reflect email verification
        final updatedUser = state.value!.user!.copyWith(
          emailConfirmedAt: DateTime.now(),
        );
        state = AsyncValue.data(
          state.value!.copyWith(user: updatedUser),
        );
      }

      return result;
    } catch (e) {
      return Result.failure(
        AppError.unknown('Email verification failed: ${e.toString()}'),
      );
    }
  }

  /// Signs in with OAuth provider.
  Future<void> signInWithOAuth({
    required OAuthProvider provider,
    String? redirectTo,
  }) async {
    try {
      state = const AsyncValue.loading();

      final repository = ref.read(webAuthRepositoryProvider);
      final result = await repository.signInWithOAuth(
        provider: provider,
        redirectTo: redirectTo,
      );

      result.when(
        success: (data) {
          final (user, session) = data;
          state = AsyncValue.data(
            WebAuthState(user: user, session: session),
          );
        },
        failure: (error) {
          state = AsyncValue.data(
            const WebAuthState().setError(error.userMessage),
          );
        },
      );
    } catch (e) {
      // OAuth flows typically redirect, so exceptions are expected
      if (e.toString().contains('OAuth flow initiated')) {
        // This is normal for OAuth flows
        return;
      }

      state = AsyncValue.data(
        const WebAuthState().setError('OAuth sign in failed: ${e.toString()}'),
      );
    }
  }

  /// Handles OAuth callback and completes authentication.
  Future<void> handleOAuthCallback() async {
    try {
      state = const AsyncValue.loading();

      final repository = ref.read(webAuthRepositoryProvider);
      final result = await repository.handleOAuthCallback();

      result.when(
        success: (data) {
          final (user, session) = data;
          state = AsyncValue.data(
            WebAuthState(user: user, session: session),
          );
        },
        failure: (error) {
          state = AsyncValue.data(
            const WebAuthState().setError(error.userMessage),
          );
        },
      );
    } catch (e) {
      state = AsyncValue.data(
        const WebAuthState().setError('OAuth callback failed: ${e.toString()}'),
      );
    }
  }

  /// Gets available OAuth providers.
  List<OAuthProvider> getAvailableOAuthProviders() {
    final repository = ref.read(webAuthRepositoryProvider);
    return repository.getAvailableOAuthProviders();
  }

  /// Restores session from storage
  Future<void> _restoreSession() async {
    try {
      final repository = ref.read(webAuthRepositoryProvider);

      // Try to get current session from Supabase
      final sessionResult = await repository.getCurrentSession();
      final userResult = await repository.getCurrentUser();

      if (sessionResult.isSuccess &&
          userResult.isSuccess &&
          sessionResult.data != null &&
          userResult.data != null) {
        final session = sessionResult.data!;
        final user = userResult.data!;

        if (!session.isExpired) {
          state = AsyncValue.data(
            WebAuthState(user: user, session: session),
          );
          return;
        }
      }

      // Try to restore from browser storage
      final restoreResult = await repository.restoreSession();
      if (restoreResult.isSuccess && restoreResult.data != null) {
        final session = restoreResult.data!;

        if (!session.isExpired) {
          // Try to refresh the session
          await refreshSession();
        }
      }
    } catch (e) {
      // Failed to restore session, start with empty state
      state = AsyncValue.data(const WebAuthState());
    }
  }
}

/// Current user provider
@riverpod
WebUserModel? currentUser(CurrentUserRef ref) {
  final authState = ref.watch(webAuthNotifierProvider);
  return authState.when(
    data: (state) => state.user,
    loading: () => null,
    error: (_, __) => null,
  );
}

/// Current session provider
@riverpod
AuthSessionModel? currentSession(CurrentSessionRef ref) {
  final authState = ref.watch(webAuthNotifierProvider);
  return authState.when(
    data: (state) => state.session,
    loading: () => null,
    error: (_, __) => null,
  );
}

/// Authentication status provider
@riverpod
bool isAuthenticated(IsAuthenticatedRef ref) {
  final authState = ref.watch(webAuthNotifierProvider);
  return authState.when(
    data: (state) => state.isAuthenticated,
    loading: () => false,
    error: (_, __) => false,
  );
}

/// Email confirmation status provider
@riverpod
bool isEmailConfirmed(IsEmailConfirmedRef ref) {
  final authState = ref.watch(webAuthNotifierProvider);
  return authState.when(
    data: (state) => state.isEmailConfirmed,
    loading: () => false,
    error: (_, __) => false,
  );
}
