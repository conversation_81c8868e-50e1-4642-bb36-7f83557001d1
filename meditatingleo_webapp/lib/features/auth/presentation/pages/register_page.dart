import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../widgets/register_form.dart';
import '../../../../core/constants/ui_constants.dart';
import '../../../../app/router/routes.dart';

/// [RegisterPage] provides the main registration interface for the web application.
///
/// This page displays the registration form in a responsive layout optimized
/// for desktop, tablet, and mobile browsers.
class RegisterPage extends ConsumerWidget {
  const RegisterPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final mediaQuery = MediaQuery.of(context);
    final isDesktop = mediaQuery.size.width >= UIConstants.desktopBreakpoint;

    return Scaffold(
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(
              isDesktop ? UIConstants.spacing32 : UIConstants.spacing16,
            ),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: isDesktop ? 400 : double.infinity,
                minHeight: mediaQuery.size.height -
                    (isDesktop ? UIConstants.spacing64 : UIConstants.spacing32),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // App logo and title
                  _buildHeader(theme, isDesktop),

                  SizedBox(
                      height: isDesktop
                          ? UIConstants.spacing48
                          : UIConstants.spacing32),

                  // Registration form
                  RegisterForm(
                    onSuccess: () => _handleRegistrationSuccess(context, ref),
                    onLogin: () => _handleLogin(context),
                  ),

                  SizedBox(height: UIConstants.spacing24),

                  // Footer
                  _buildFooter(theme),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isDesktop) {
    return Column(
      children: [
        // App icon/logo
        Container(
          width: isDesktop ? 80 : 64,
          height: isDesktop ? 80 : 64,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary,
            borderRadius: BorderRadius.circular(UIConstants.borderRadius16),
          ),
          child: Icon(
            Icons.self_improvement,
            size: isDesktop ? 48 : 36,
            color: theme.colorScheme.onPrimary,
          ),
        ),

        SizedBox(height: UIConstants.spacing16),

        // App title
        Text(
          'Join ClarityByMeditatingLeo',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: UIConstants.spacing8),

        // Subtitle
        Text(
          'Start your journey to clarity and mindfulness',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildFooter(ThemeData theme) {
    return Column(
      children: [
        // Divider
        Divider(
          color: theme.colorScheme.outline.withValues(alpha: 0.3),
        ),

        SizedBox(height: UIConstants.spacing16),

        // Footer text
        Text(
          'By creating an account, you agree to our Terms of Service and Privacy Policy.',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  void _handleRegistrationSuccess(BuildContext context, WidgetRef ref) {
    final authState = ref.read(webAuthNotifierProvider);
    final isAuthenticated = authState.when(
      data: (state) => state.isAuthenticated,
      loading: () => false,
      error: (_, __) => false,
    );

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
            'Account created successfully! Please check your email to verify your account.'),
        behavior: SnackBarBehavior.floating,
        duration: Duration(seconds: 5),
      ),
    );

    if (isAuthenticated) {
      // User is auto-logged in (email confirmation disabled)
      context.go(AppRoutes.home);
    } else {
      // Email confirmation required - redirect to login
      context.go(AppRoutes.login);
    }
  }

  void _handleLogin(BuildContext context) {
    // Navigate to login page
    context.go(AppRoutes.login);
  }
}
